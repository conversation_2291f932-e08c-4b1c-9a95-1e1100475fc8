/**
 ******************************************************************************
 * @file    ad9834_highperf.c
 * <AUTHOR>
 * @version V2.0
 * @date    2025-07-31
 * @brief   AD9834高性能DDS信号发生器驱动实现
 *          专为3MHz+高频信号生成优化设计
 ******************************************************************************
 */

#include "ad9834_highperf.h"
#include "delay.h"

/* ==================== 私有变量 ==================== */
static AD9834_Config_t current_config = {
    .frequency_hz = 1000000,        // 默认1MHz
    .phase_deg = 0,                 // 默认0度相位
    .wave_type = AD9834_WAVE_SINE,  // 默认正弦波
    .freq_reg = AD9834_FREQ_REG0,   // 默认使用频率寄存器0
    .phase_reg = AD9834_PHASE_REG0, // 默认使用相位寄存器0
    .enable_pin_control = 0         // 默认软件控制
};

/* ==================== 私有函数声明 ==================== */
static void AD9834_GPIO_Config(void);
static void AD9834_Delay_ns(uint16_t ns);
static uint32_t AD9834_CalcFreqWord(uint32_t frequency_hz);
static uint16_t AD9834_CalcPhaseWord(uint16_t phase_deg);

/**
 * @brief  AD9834高性能初始化
 * @param  None
 * @retval None
 */
void AD9834_HighPerf_Init(void)
{
    // 配置GPIO
    AD9834_GPIO_Config();
    
    // 硬件复位
    AD9834_HardReset();
    
    // 软件复位和初始化序列
    AD9834_Write16Bits_Fast(AD9834_B28 | AD9834_RESET);  // 使能B28位，复位内部寄存器
    AD9834_Delay_ns(100);  // 等待复位完成
    
    // 设置默认配置
    AD9834_Configure(&current_config);
    
    // 清除复位位，开始正常工作
    AD9834_Write16Bits_Fast(AD9834_B28);
}

/**
 * @brief  AD9834 GPIO配置 (高速优化)
 * @param  None
 * @retval None
 */
static void AD9834_GPIO_Config(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA | RCC_AHB1Periph_GPIOB, ENABLE);
    
    // 配置控制信号引脚 (PA3-PA6)
    GPIO_InitStructure.GPIO_Pin = AD9834_FSYNC_PIN | AD9834_SCLK_PIN | 
                                  AD9834_SDATA_PIN | AD9834_RESET_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;  // 最高速度
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(AD9834_CTRL_PORT, &GPIO_InitStructure);
    
    // 配置选择信号引脚 (PB0-PB1)
    GPIO_InitStructure.GPIO_Pin = AD9834_FSELECT_PIN | AD9834_PSELECT_PIN;
    GPIO_Init(AD9834_SEL_PORT, &GPIO_InitStructure);
    
    // 设置初始状态
    AD9834_FSYNC_HIGH();    // FSYNC默认高电平
    AD9834_SCLK_HIGH();     // SCLK默认高电平
    AD9834_SDATA_HIGH();    // SDATA默认高电平
    AD9834_RESET_HIGH();    // RESET默认高电平
    AD9834_FSELECT_LOW();   // 选择频率寄存器0
    AD9834_PSELECT_LOW();   // 选择相位寄存器0
}

/**
 * @brief  AD9834写入16位数据 (高速优化版本)
 * @param  data: 要写入的16位数据
 * @retval None
 */
void AD9834_Write16Bits_Fast(uint16_t data)
{
    uint8_t i;
    
    // 开始传输
    AD9834_SCLK_HIGH();
    AD9834_FSYNC_LOW();     // 拉低FSYNC开始传输
    
    // 传输16位数据 (MSB先传)
    for (i = 0; i < 16; i++) {
        // 设置数据位
        if (data & 0x8000) {
            AD9834_SDATA_HIGH();
        } else {
            AD9834_SDATA_LOW();
        }
        
        // 时钟下降沿 (数据锁存)
        AD9834_SCLK_LOW();
        AD9834_Delay_ns(50);    // 最小时钟低电平时间
        
        // 时钟上升沿
        AD9834_SCLK_HIGH();
        AD9834_Delay_ns(50);    // 最小时钟高电平时间
        
        data <<= 1;  // 移位到下一位
    }
    
    // 结束传输
    AD9834_SDATA_HIGH();    // 数据线拉高
    AD9834_FSYNC_HIGH();    // 拉高FSYNC结束传输
}

/**
 * @brief  设置AD9834输出频率 (高精度版本)
 * @param  freq_reg: 频率寄存器选择
 * @param  frequency_hz: 频率值 (Hz)
 * @retval None
 */
void AD9834_SetFrequency_HighPrec(AD9834_FreqReg_t freq_reg, uint32_t frequency_hz)
{
    uint32_t freq_word;
    uint16_t freq_lsb, freq_msb;
    uint16_t reg_base;
    
    // 限制频率范围
    if (frequency_hz > AD9834_MAX_FREQ_HZ) {
        frequency_hz = AD9834_MAX_FREQ_HZ;
    }
    
    // 计算频率控制字
    freq_word = AD9834_CalcFreqWord(frequency_hz);
    
    // 分离LSB和MSB
    freq_lsb = (uint16_t)(freq_word & 0x3FFF);        // 低14位
    freq_msb = (uint16_t)((freq_word >> 14) & 0x3FFF); // 高14位
    
    // 确定寄存器基址
    if (freq_reg == AD9834_FREQ_REG0) {
        reg_base = AD9834_FREQ0_LSB;
    } else {
        reg_base = AD9834_FREQ1_LSB;
    }
    
    // 写入频率寄存器 (LSB先写，MSB后写)
    AD9834_Write16Bits_Fast(reg_base | freq_lsb);
    AD9834_Write16Bits_Fast(reg_base | freq_msb);
    
    // 更新当前配置
    current_config.frequency_hz = frequency_hz;
    current_config.freq_reg = freq_reg;
}

/**
 * @brief  设置AD9834相位 (高精度版本)
 * @param  phase_reg: 相位寄存器选择
 * @param  phase_deg: 相位值 (度)
 * @retval None
 */
void AD9834_SetPhase_HighPrec(AD9834_PhaseReg_t phase_reg, uint16_t phase_deg)
{
    uint16_t phase_word;
    uint16_t reg_addr;
    
    // 限制相位范围 (0-359度)
    phase_deg = phase_deg % 360;
    
    // 计算相位控制字
    phase_word = AD9834_CalcPhaseWord(phase_deg);
    
    // 确定寄存器地址
    if (phase_reg == AD9834_PHASE_REG0) {
        reg_addr = AD9834_PHASE0;
    } else {
        reg_addr = AD9834_PHASE1;
    }
    
    // 写入相位寄存器
    AD9834_Write16Bits_Fast(reg_addr | phase_word);
    
    // 更新当前配置
    current_config.phase_deg = phase_deg;
    current_config.phase_reg = phase_reg;
}

/**
 * @brief  设置AD9834波形类型
 * @param  wave_type: 波形类型
 * @retval None
 */
void AD9834_SetWaveType(AD9834_WaveType_t wave_type)
{
    // 写入控制寄存器设置波形类型
    AD9834_Write16Bits_Fast(wave_type);
    
    // 更新当前配置
    current_config.wave_type = wave_type;
}

/**
 * @brief  AD9834完整配置 (一次性设置所有参数)
 * @param  config: 配置结构体指针
 * @retval None
 */
void AD9834_Configure(const AD9834_Config_t* config)
{
    // 设置频率
    AD9834_SetFrequency_HighPrec(config->freq_reg, config->frequency_hz);
    
    // 设置相位
    AD9834_SetPhase_HighPrec(config->phase_reg, config->phase_deg);
    
    // 设置波形类型
    AD9834_SetWaveType(config->wave_type);
    
    // 设置引脚控制模式
    if (config->enable_pin_control) {
        // 启用引脚控制
        AD9834_Write16Bits_Fast(AD9834_B28 | AD9834_PIN_SW);
    } else {
        // 软件控制模式
        AD9834_Write16Bits_Fast(AD9834_B28);
    }
    
    // 更新当前配置
    current_config = *config;
}

/**
 * @brief  AD9834快速频率切换 (用于扫频)
 * @param  freq1_hz: 频率1 (Hz)
 * @param  freq2_hz: 频率2 (Hz)
 * @param  switch_time_us: 切换时间间隔 (微秒)
 * @retval None
 */
void AD9834_FastFreqSweep(uint32_t freq1_hz, uint32_t freq2_hz, uint16_t switch_time_us)
{
    // 预设置两个频率寄存器
    AD9834_SetFrequency_HighPrec(AD9834_FREQ_REG0, freq1_hz);
    AD9834_SetFrequency_HighPrec(AD9834_FREQ_REG1, freq2_hz);
    
    // 启用引脚控制模式进行快速切换
    AD9834_Write16Bits_Fast(AD9834_B28 | AD9834_PIN_SW);
    
    // 快速切换频率 (通过FSELECT引脚)
    while (1) {
        AD9834_FSELECT_LOW();   // 选择频率寄存器0 (freq1)
        Delay_us(switch_time_us);
        
        AD9834_FSELECT_HIGH();  // 选择频率寄存器1 (freq2)
        Delay_us(switch_time_us);
    }
}

/**
 * @brief  AD9834软件复位
 * @param  None
 * @retval None
 */
void AD9834_SoftReset(void)
{
    AD9834_Write16Bits_Fast(AD9834_B28 | AD9834_RESET);
    AD9834_Delay_ns(100);
    AD9834_Write16Bits_Fast(AD9834_B28);
}

/**
 * @brief  AD9834硬件复位
 * @param  None
 * @retval None
 */
void AD9834_HardReset(void)
{
    AD9834_RESET_LOW();
    Delay_us(10);  // 保持复位10微秒
    AD9834_RESET_HIGH();
    Delay_us(10);  // 等待复位完成
}

/**
 * @brief  获取AD9834状态信息
 * @param  config: 返回当前配置信息
 * @retval None
 */
void AD9834_GetStatus(AD9834_Config_t* config)
{
    *config = current_config;
}

/* ==================== 私有函数实现 ==================== */

/**
 * @brief  纳秒级延时 (高精度时序控制)
 * @param  ns: 延时时间 (纳秒)
 * @retval None
 */
static void AD9834_Delay_ns(uint16_t ns)
{
    // 基于168MHz系统时钟的精确延时
    // 1个时钟周期 = 5.95ns
    uint32_t cycles = (ns * 168) / 1000;  // 转换为时钟周期数
    
    while (cycles--) {
        __NOP();  // 空操作指令
    }
}

/**
 * @brief  计算频率控制字
 * @param  frequency_hz: 目标频率 (Hz)
 * @retval 28位频率控制字
 */
static uint32_t AD9834_CalcFreqWord(uint32_t frequency_hz)
{
    // 频率控制字计算公式:
    // freq_word = (frequency_hz * 2^28) / AD9834_SYSTEM_CLOCK_HZ
    // 使用64位运算避免溢出
    uint64_t temp = (uint64_t)frequency_hz * 268435456ULL;  // 2^28 = 268435456
    return (uint32_t)(temp / AD9834_SYSTEM_CLOCK_HZ);
}

/**
 * @brief  计算相位控制字
 * @param  phase_deg: 目标相位 (度)
 * @retval 12位相位控制字
 */
static uint16_t AD9834_CalcPhaseWord(uint16_t phase_deg)
{
    // 相位控制字计算公式:
    // phase_word = (phase_deg * 4096) / 360
    // 4096 = 2^12 (12位分辨率)
    return (uint16_t)((phase_deg * 4096) / 360) & 0x0FFF;
}
