/**
 ******************************************************************************
 * @file    ad9834_highperf.h
 * <AUTHOR>
 * @version V2.0
 * @date    2025-07-31
 * @brief   AD9834高性能DDS信号发生器驱动头文件
 *          专为3MHz+高频信号生成优化设计
 ******************************************************************************
 * @attention
 * 
 * 硬件连接 (STM32F407VGT6 "天空星"开发板):
 * PA3  --> AD9834_FSYNC   (帧同步信号)
 * PA4  --> AD9834_SCLK    (串行时钟)
 * PA5  --> AD9834_SDATA   (串行数据)
 * PA6  --> AD9834_RESET   (复位信号)
 * PB0  --> AD9834_FSELECT (频率选择)
 * PB1  --> AD9834_PSELECT (相位选择)
 * 
 * 性能指标:
 * - 最大输出频率: 37.5MHz
 * - 频率分辨率: 0.028Hz
 * - 相位分辨率: 0.088°
 * - SFDR: >72dB @1MHz
 * - 设置时间: <1μs
 ******************************************************************************
 */

#ifndef __AD9834_HIGHPERF_H
#define __AD9834_HIGHPERF_H

#include "stm32f4xx.h"
#include <math.h>

/* ==================== AD9834技术规格 ==================== */
#define AD9834_MAX_FREQ_HZ          37500000UL  // 37.5MHz最大输出频率
#define AD9834_SYSTEM_CLOCK_HZ      75000000UL  // 75MHz系统时钟
#define AD9834_FREQ_RESOLUTION_HZ   0.028f      // 频率分辨率
#define AD9834_PHASE_RESOLUTION_DEG 0.088f      // 相位分辨率

/* ==================== 硬件引脚定义 ==================== */
// 控制信号引脚 (GPIOA)
#define AD9834_CTRL_PORT            GPIOA
#define AD9834_FSYNC_PIN            GPIO_Pin_3  // PA3 - 帧同步
#define AD9834_SCLK_PIN             GPIO_Pin_4  // PA4 - 串行时钟
#define AD9834_SDATA_PIN            GPIO_Pin_5  // PA5 - 串行数据
#define AD9834_RESET_PIN            GPIO_Pin_6  // PA6 - 复位信号

// 选择信号引脚 (GPIOB)
#define AD9834_SEL_PORT             GPIOB
#define AD9834_FSELECT_PIN          GPIO_Pin_0  // PB0 - 频率选择
#define AD9834_PSELECT_PIN          GPIO_Pin_1  // PB1 - 相位选择

/* ==================== 快速GPIO操作宏 ==================== */
#define AD9834_FSYNC_HIGH()         GPIO_SetBits(AD9834_CTRL_PORT, AD9834_FSYNC_PIN)
#define AD9834_FSYNC_LOW()          GPIO_ResetBits(AD9834_CTRL_PORT, AD9834_FSYNC_PIN)
#define AD9834_SCLK_HIGH()          GPIO_SetBits(AD9834_CTRL_PORT, AD9834_SCLK_PIN)
#define AD9834_SCLK_LOW()           GPIO_ResetBits(AD9834_CTRL_PORT, AD9834_SCLK_PIN)
#define AD9834_SDATA_HIGH()         GPIO_SetBits(AD9834_CTRL_PORT, AD9834_SDATA_PIN)
#define AD9834_SDATA_LOW()          GPIO_ResetBits(AD9834_CTRL_PORT, AD9834_SDATA_PIN)
#define AD9834_RESET_HIGH()         GPIO_SetBits(AD9834_CTRL_PORT, AD9834_RESET_PIN)
#define AD9834_RESET_LOW()          GPIO_ResetBits(AD9834_CTRL_PORT, AD9834_RESET_PIN)

#define AD9834_FSELECT_HIGH()       GPIO_SetBits(AD9834_SEL_PORT, AD9834_FSELECT_PIN)
#define AD9834_FSELECT_LOW()        GPIO_ResetBits(AD9834_SEL_PORT, AD9834_FSELECT_PIN)
#define AD9834_PSELECT_HIGH()       GPIO_SetBits(AD9834_SEL_PORT, AD9834_PSELECT_PIN)
#define AD9834_PSELECT_LOW()        GPIO_ResetBits(AD9834_SEL_PORT, AD9834_PSELECT_PIN)

/* ==================== AD9834寄存器定义 ==================== */
// 控制寄存器位定义
#define AD9834_REG_CTRL             0x2000  // 控制寄存器基址
#define AD9834_B28                  0x2000  // B28位：28位频率写入
#define AD9834_HLB                  0x1000  // HLB位：MSB/LSB选择
#define AD9834_FSELECT              0x0800  // FSELECT位：频率寄存器选择
#define AD9834_PSELECT              0x0400  // PSELECT位：相位寄存器选择
#define AD9834_PIN_SW               0x0200  // PIN/SW位：引脚/软件控制
#define AD9834_RESET                0x0100  // RESET位：内部复位
#define AD9834_SLEEP1               0x0080  // SLEEP1位：DAC省电
#define AD9834_SLEEP12              0x0040  // SLEEP12位：内部时钟省电
#define AD9834_OPBITEN              0x0020  // OPBITEN位：输出位使能
#define AD9834_SIGN_PIB             0x0010  // SIGN/PIB位：符号位输出
#define AD9834_DIV2                 0x0008  // DIV2位：除2功能
#define AD9834_MODE                 0x0002  // MODE位：三角波/正弦波选择

// 频率寄存器
#define AD9834_FREQ0_LSB            0x4000  // 频率寄存器0 LSB
#define AD9834_FREQ0_MSB            0x4000  // 频率寄存器0 MSB
#define AD9834_FREQ1_LSB            0x8000  // 频率寄存器1 LSB
#define AD9834_FREQ1_MSB            0x8000  // 频率寄存器1 MSB

// 相位寄存器
#define AD9834_PHASE0               0xC000  // 相位寄存器0
#define AD9834_PHASE1               0xE000  // 相位寄存器1

/* ==================== 波形类型定义 ==================== */
typedef enum {
    AD9834_WAVE_SINE     = 0x2000,  // 正弦波
    AD9834_WAVE_TRIANGLE = 0x2002,  // 三角波
    AD9834_WAVE_SQUARE   = 0x2028,  // 方波 (MSB)
    AD9834_WAVE_SQUARE2  = 0x2020   // 方波 (MSB/2)
} AD9834_WaveType_t;

/* ==================== 频率寄存器选择 ==================== */
typedef enum {
    AD9834_FREQ_REG0 = 0,  // 频率寄存器0
    AD9834_FREQ_REG1 = 1   // 频率寄存器1
} AD9834_FreqReg_t;

/* ==================== 相位寄存器选择 ==================== */
typedef enum {
    AD9834_PHASE_REG0 = 0,  // 相位寄存器0
    AD9834_PHASE_REG1 = 1   // 相位寄存器1
} AD9834_PhaseReg_t;

/* ==================== AD9834配置结构体 ==================== */
typedef struct {
    uint32_t frequency_hz;          // 输出频率 (Hz)
    uint16_t phase_deg;             // 相位 (度)
    AD9834_WaveType_t wave_type;    // 波形类型
    AD9834_FreqReg_t freq_reg;      // 使用的频率寄存器
    AD9834_PhaseReg_t phase_reg;    // 使用的相位寄存器
    uint8_t enable_pin_control;     // 是否启用引脚控制
} AD9834_Config_t;

/* ==================== 函数声明 ==================== */

/**
 * @brief  AD9834高性能初始化
 * @param  None
 * @retval None
 */
void AD9834_HighPerf_Init(void);

/**
 * @brief  AD9834写入16位数据 (优化版本)
 * @param  data: 要写入的16位数据
 * @retval None
 */
void AD9834_Write16Bits_Fast(uint16_t data);

/**
 * @brief  设置AD9834输出频率 (高精度版本)
 * @param  freq_reg: 频率寄存器选择
 * @param  frequency_hz: 频率值 (Hz)
 * @retval None
 */
void AD9834_SetFrequency_HighPrec(AD9834_FreqReg_t freq_reg, uint32_t frequency_hz);

/**
 * @brief  设置AD9834相位 (高精度版本)
 * @param  phase_reg: 相位寄存器选择
 * @param  phase_deg: 相位值 (度)
 * @retval None
 */
void AD9834_SetPhase_HighPrec(AD9834_PhaseReg_t phase_reg, uint16_t phase_deg);

/**
 * @brief  设置AD9834波形类型
 * @param  wave_type: 波形类型
 * @retval None
 */
void AD9834_SetWaveType(AD9834_WaveType_t wave_type);

/**
 * @brief  AD9834完整配置 (一次性设置所有参数)
 * @param  config: 配置结构体指针
 * @retval None
 */
void AD9834_Configure(const AD9834_Config_t* config);

/**
 * @brief  AD9834快速频率切换 (用于扫频)
 * @param  freq1_hz: 频率1 (Hz)
 * @param  freq2_hz: 频率2 (Hz)
 * @param  switch_time_us: 切换时间间隔 (微秒)
 * @retval None
 */
void AD9834_FastFreqSweep(uint32_t freq1_hz, uint32_t freq2_hz, uint16_t switch_time_us);

/**
 * @brief  AD9834软件复位
 * @param  None
 * @retval None
 */
void AD9834_SoftReset(void);

/**
 * @brief  AD9834硬件复位
 * @param  None
 * @retval None
 */
void AD9834_HardReset(void);

/**
 * @brief  获取AD9834状态信息
 * @param  config: 返回当前配置信息
 * @retval None
 */
void AD9834_GetStatus(AD9834_Config_t* config);

#endif /* __AD9834_HIGHPERF_H */
