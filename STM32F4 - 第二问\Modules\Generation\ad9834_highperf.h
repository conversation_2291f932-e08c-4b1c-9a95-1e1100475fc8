/**
 ******************************************************************************
 * @file    ad9834_highperf.h
 * <AUTHOR> - AD9834方案
 * @version V3.0
 * @date    2025-07-31
 * @brief   AD9834高性能DDS信号发生器驱动 - 替换内置DAC方案
 *          基于商家提供的AD9834模块驱动优化，实现1Hz-5MHz高精度输出
 ******************************************************************************
 * @attention
 *
 * 硬件连接 (嘉立创"天空星"STM32F407VGT6开发板 + AD9834模块):
 * PA3  --> AD9834_FSYNC   (帧同步信号)
 * PA4  --> AD9834_SCLK    (串行时钟)
 * PA5  --> AD9834_SDATA   (串行数据)
 * PA6  --> AD9834_RESET   (复位信号)
 * PB0  --> AD9834_FSELECT (频率选择 - 硬件切换)
 * PB1  --> AD9834_PSELECT (相位选择 - 硬件切换)
 *
 * AD9834技术规格:
 * - 系统时钟: 75MHz
 * - 最大输出频率: 37.5MHz (实际使用1Hz-5MHz)
 * - 频率分辨率: 0.028Hz (28位频率字)
 * - 相位分辨率: 0.088° (12位相位字)
 * - SFDR: >72dB @1MHz
 * - 输出幅度: 650mVpp (典型值)
 ******************************************************************************
 */

#ifndef __AD9834_HIGHPERF_H
#define __AD9834_HIGHPERF_H

#include "stm32f4xx.h"
#include "delay.h"

/* ==================== AD9834技术规格 (基于商家资料) ==================== */
#define AD9834_SYSTEM_CLOCK         75000000UL  // 75MHz系统时钟
#define AD9834_MAX_FREQ_HZ          5000000UL   // 5MHz最大使用频率 (项目需求)
#define AD9834_MIN_FREQ_HZ          1UL         // 1Hz最小频率
#define AD9834_FREQ_RESOLUTION_HZ   0.028f      // 频率分辨率 (75MHz/2^28)

/* ==================== 硬件引脚定义 (基于商家接线说明) ==================== */
// 控制信号引脚 (GPIOA) - 与商家驱动保持一致
#define AD9834_Control_Port         GPIOA
#define AD9834_FSYNC                GPIO_Pin_3  // PA3 - 帧同步信号
#define AD9834_SCLK                 GPIO_Pin_4  // PA4 - 串行时钟
#define AD9834_SDATA                GPIO_Pin_5  // PA5 - 串行数据
#define AD9834_RESET                GPIO_Pin_6  // PA6 - 复位信号

// 选择信号引脚 (GPIOB) - 硬件频率/相位切换
#define AD9834_FS                   GPIO_Pin_0  // PB0 - 频率选择 (FREQ0/FREQ1)
#define AD9834_PS                   GPIO_Pin_1  // PB1 - 相位选择 (PHASE0/PHASE1)

/* ==================== 快速GPIO操作宏 (兼容商家驱动) ==================== */
#define AD9834_FSYNC_SET            GPIO_SetBits(AD9834_Control_Port, AD9834_FSYNC)
#define AD9834_FSYNC_CLR            GPIO_ResetBits(AD9834_Control_Port, AD9834_FSYNC)
#define AD9834_SCLK_SET             GPIO_SetBits(AD9834_Control_Port, AD9834_SCLK)
#define AD9834_SCLK_CLR             GPIO_ResetBits(AD9834_Control_Port, AD9834_SCLK)
#define AD9834_SDATA_SET            GPIO_SetBits(AD9834_Control_Port, AD9834_SDATA)
#define AD9834_SDATA_CLR            GPIO_ResetBits(AD9834_Control_Port, AD9834_SDATA)
#define AD9834_RESET_SET            GPIO_SetBits(AD9834_Control_Port, AD9834_RESET)
#define AD9834_RESET_CLR            GPIO_ResetBits(AD9834_Control_Port, AD9834_RESET)

#define AD9834_FS_SET               GPIO_SetBits(GPIOB, AD9834_FS)
#define AD9834_FS_CLR               GPIO_ResetBits(GPIOB, AD9834_FS)
#define AD9834_PS_SET               GPIO_SetBits(GPIOB, AD9834_PS)
#define AD9834_PS_CLR               GPIO_ResetBits(GPIOB, AD9834_PS)

/* ==================== AD9834寄存器定义 (基于商家驱动) ==================== */
// 波形类型定义 - 与商家驱动保持一致
#define TRIANGLE_WAVE               0x2002  // 三角波
#define SINE_WAVE                   0x2008  // 正弦波 (默认)
#define SQUARE_WAVE                 0x2028  // 方波
#define PIN_SW                      0x0200  // 引脚/软件控制位

// 频率寄存器定义
#define FREQ_REG_0                  0x4000  // 频率寄存器0
#define FREQ_REG_1                  0x8000  // 频率寄存器1

// 相位寄存器定义
#define PHASE_REG_0                 0xC000  // 相位寄存器0
#define PHASE_REG_1                 0xE000  // 相位寄存器1

// 控制寄存器位定义
#define AD9834_B28                  0x2000  // B28位：28位频率写入
#define AD9834_RESET_BIT            0x0100  // RESET位：内部复位
#define AD9834_SLEEP1               0x0080  // SLEEP1位：DAC省电模式
#define AD9834_SLEEP12              0x0040  // SLEEP12位：内部时钟省电
#define AD9834_OPBITEN              0x0020  // OPBITEN位：输出位使能
#define AD9834_DIV2                 0x0008  // DIV2位：除2功能 (幅度减半)

/* ==================== 函数声明 (基于商家驱动优化) ==================== */

/**
 * @brief  AD9834初始化 (替换内置DAC方案)
 * @param  None
 * @retval None
 */
void AD9834_Init(void);

/**
 * @brief  AD9834写入16位数据 (高速优化)
 * @param  data: 要写入的16位数据
 * @retval None
 */
void AD9834_Write_16Bits(uint32_t data);

/**
 * @brief  设置AD9834频率 (高精度1Hz-5MHz)
 * @param  reg: 频率寄存器选择 (FREQ_REG_0 或 FREQ_REG_1)
 * @param  frequency_hz: 频率值 (1Hz - 5MHz)
 * @param  wave_type: 波形类型 (SINE_WAVE, TRIANGLE_WAVE, SQUARE_WAVE)
 * @retval None
 */
void AD9834_SetFrequency(uint16_t reg, float frequency_hz, uint16_t wave_type);

/**
 * @brief  设置AD9834相位
 * @param  reg: 相位寄存器选择 (PHASE_REG_0 或 PHASE_REG_1)
 * @param  phase_value: 相位值 (0-4095, 对应0°-360°)
 * @retval None
 */
void AD9834_SetPhase(uint16_t reg, uint16_t phase_value);

/**
 * @brief  快速频率切换 (用于扫频测试)
 * @param  freq1_hz: 频率1 (Hz)
 * @param  freq2_hz: 频率2 (Hz)
 * @retval None
 */
void AD9834_FastFreqSwitch(float freq1_hz, float freq2_hz);

/**
 * @brief  AD9834复位
 * @param  None
 * @retval None
 */
void AD9834_Reset(void);



#endif /* __AD9834_HIGHPERF_H */
