# 电赛G题幅度控制解决方案

## 🎯 问题分析

### 当前状态：
- **AD9834输出**：210mV峰峰值，频率1Hz-5MHz可控
- **G题要求**：1-3V峰峰值可控，满足不同测试需求

### 目标实现：
- **基本要求**：≥3V峰峰值输出
- **控制要求**：2V峰峰值精确控制
- **扫描要求**：1-2V连续可调，步长0.1V

## 🔧 方案1：数控可变增益放大器 (推荐)

### 硬件设计：

```
AD9834(210mV) → 可变增益放大器 → 输出(1-3V可控)
                      ↑
                 STM32 DAC控制
```

### 电路原理图：

```
AD9834_OUT ──┬── R1(1kΩ) ──┬── U1A(+)
             │              │
             └── C1(100nF)  │
                            │
STM32_DAC ───── R2(10kΩ) ──┴── U1A(-)
                            │
                            │
                     R3(可变) 
                            │
                            └── U1A_OUT → 输出
```

### 核心器件：
- **运放**：LM358或TL072 (双运放)
- **控制**：STM32内置DAC (PA4或PA5)
- **增益范围**：5倍-15倍 (210mV → 1-3V)

## 🔧 方案2：数字电位器控制 (精确)

### 硬件设计：

```
AD9834 → 固定放大器 → 数字电位器衰减 → 输出
                           ↑
                      STM32 SPI控制
```

### 推荐器件：
- **数字电位器**：MCP4131 (SPI接口，129档位)
- **固定放大器**：LM358，增益15倍 (210mV → 3.15V)
- **控制精度**：3.15V/129 ≈ 24mV步长

## 💻 软件实现方案

### 方案1软件实现：

```c
// 幅度控制函数
void SetAmplitude_V(float target_voltage)
{
    // 计算所需DAC值
    // AD9834: 210mV → 目标电压
    float gain_needed = target_voltage / 0.21f;  // 所需增益
    
    // 限制增益范围 (5-15倍)
    if (gain_needed < 5.0f) gain_needed = 5.0f;
    if (gain_needed > 15.0f) gain_needed = 15.0f;
    
    // 转换为DAC值 (假设0-3.3V对应增益5-15倍)
    uint16_t dac_value = (uint16_t)((gain_needed - 5.0f) * 4095.0f / 10.0f);
    
    // 输出到DAC
    DAC_SetChannel1Data(DAC_Align_12b_R, dac_value);
}

// G题专用函数
void G_SetSignalGenerator(uint32_t freq_hz, float amplitude_v)
{
    // 设置频率
    AD9834_SetFrequency(FREQ_REG_0, (float)freq_hz, SINE_WAVE);
    
    // 设置幅度
    SetAmplitude_V(amplitude_v);
    
    // 稳定时间
    Delay_ms(10);
}
```

## 🎯 G题具体实现

### 基本要求实现：

```c
// 信号发生器：100Hz-1MHz，≥3V峰峰值
void G_BasicRequirement_SignalGenerator(void)
{
    // 设置3V峰峰值输出
    G_SetSignalGenerator(1000000, 3.0f);  // 1MHz, 3V
    
    // 频率扫描测试
    for (uint32_t freq = 100; freq <= 1000000; freq += 100) {
        G_SetSignalGenerator(freq, 3.0f);
        Delay_ms(10);  // 每个频率停留10ms
    }
}
```

### 控制要求实现：

```c
// 控制已知电路：1kHz，2V峰峰值
void G_ControlRequirement(void)
{
    // 设置1kHz，2V峰峰值
    G_SetSignalGenerator(1000, 2.0f);
    
    // 监控已知电路输出，调整幅度使其输出2V
    // (需要配合ADC采集反馈)
}
```

### 扫描要求实现：

```c
// 频率扫描：100Hz-3kHz，1-2V可调
void G_ScanRequirement(void)
{
    for (uint32_t freq = 100; freq <= 3000; freq += 100) {
        for (float voltage = 1.0f; voltage <= 2.0f; voltage += 0.1f) {
            G_SetSignalGenerator(freq, voltage);
            Delay_ms(100);  // 每个设置停留100ms
            
            // 这里可以进行测量和记录
        }
    }
}
```

## 🔌 硬件连接方案

### STM32引脚分配：

```
// AD9834控制 (已有)
PA3 → AD9834_FSYNC
PA4 → AD9834_SCLK  
PA5 → AD9834_SDATA
PA6 → AD9834_RESET

// 幅度控制 (新增)
PA4 → DAC_OUT1 (幅度控制电压)
PA0 → ADC_IN0  (反馈监控)

// 已知电路接口
PB0 → 已知电路输入
PB1 → 已知电路输出监控
```

### 完整信号链：

```
STM32 → AD9834 → 可变增益放大器 → 已知电路 → ADC反馈
  ↓         ↓           ↑                    ↑
 SPI      210mV    DAC控制电压           输出监控
```

## 📊 性能指标预期

### 幅度控制精度：
- **控制范围**：1.0V - 3.0V
- **控制精度**：±0.05V (DAC 12位精度)
- **稳定性**：±1% (运放精度)

### 频率控制精度：
- **控制范围**：100Hz - 1MHz
- **控制精度**：±0.01% (AD9834精度)
- **步长精度**：100Hz精确步长

### 系统响应时间：
- **频率切换**：<1ms
- **幅度切换**：<5ms
- **系统稳定**：<10ms

## 🛠️ 实施步骤

### 第一步：硬件搭建
1. **制作可变增益放大器电路**
2. **连接STM32 DAC输出**
3. **验证放大器增益范围**

### 第二步：软件开发
1. **编写DAC控制函数**
2. **编写幅度设置函数**
3. **集成频率+幅度控制**

### 第三步：G题功能实现
1. **基本要求验证**
2. **控制要求实现**
3. **扫描要求完成**

### 第四步：精度校准
1. **用示波器校准幅度**
2. **用频率计校准频率**
3. **优化控制算法**

## 💡 备选方案

### 如果运放方案复杂：
1. **简单电阻分压**：固定几档幅度
2. **继电器切换**：多档位增益选择
3. **PWM滤波**：数字控制模拟输出

### 如果精度要求更高：
1. **16位DAC**：外接高精度DAC
2. **闭环控制**：ADC反馈自动调节
3. **温度补偿**：温度传感器校正

---

**🎯 通过这个方案，我们可以在AD9834固定幅度的基础上，实现G题要求的可控幅度输出，完成所有基本要求！**

**下一步建议：先实现简单的可变增益放大器，验证幅度控制效果。**
