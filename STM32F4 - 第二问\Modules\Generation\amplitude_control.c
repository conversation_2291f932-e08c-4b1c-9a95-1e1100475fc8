/**
 ******************************************************************************
 * @file    amplitude_control.c
 * <AUTHOR> - G题幅度控制
 * @version V1.0
 * @date    2025-07-31
 * @brief   信号幅度控制驱动实现 (配合AD9834实现G题要求)
 ******************************************************************************
 */

#include "amplitude_control.h"
#include "ad9834_highperf.h"
#include "delay.h"

/* ==================== 私有变量 ==================== */
static float current_voltage = 1.0f;  // 当前设置的输出电压

/**
 * @brief  幅度控制系统初始化
 * @param  None
 * @retval None
 */
void AmplitudeControl_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    DAC_InitTypeDef DAC_InitStructure;
    
    // 使能时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_DAC, ENABLE);
    
    // 配置PA4为DAC1_OUT
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    // 配置DAC
    DAC_InitStructure.DAC_Trigger = DAC_Trigger_None;
    DAC_InitStructure.DAC_WaveGeneration = DAC_WaveGeneration_None;
    DAC_InitStructure.DAC_OutputBuffer = DAC_OutputBuffer_Enable;
    DAC_Init(DAC_Channel_1, &DAC_InitStructure);
    
    // 使能DAC
    DAC_Cmd(DAC_Channel_1, ENABLE);
    
    // 设置默认幅度 (1V)
    AmplitudeControl_SetVoltage(1.0f);
}

/**
 * @brief  设置输出信号幅度
 * @param  target_voltage: 目标输出电压 (1.0V - 3.0V)
 * @retval 实际设置的电压值
 */
float AmplitudeControl_SetVoltage(float target_voltage)
{
    // 限制电压范围
    if (target_voltage < AMP_CTRL_MIN_VOLTAGE) target_voltage = AMP_CTRL_MIN_VOLTAGE;
    if (target_voltage > AMP_CTRL_MAX_VOLTAGE) target_voltage = AMP_CTRL_MAX_VOLTAGE;
    
    // 计算所需增益
    float gain_needed = target_voltage / AMP_CTRL_AD9834_OUTPUT;
    
    // 限制增益范围
    if (gain_needed < AMP_CTRL_MIN_GAIN) gain_needed = AMP_CTRL_MIN_GAIN;
    if (gain_needed > AMP_CTRL_MAX_GAIN) gain_needed = AMP_CTRL_MAX_GAIN;
    
    // 转换为DAC值
    // DAC 0V → 增益5倍，DAC 3.3V → 增益15倍
    float dac_voltage = (gain_needed - AMP_CTRL_MIN_GAIN) * 3.3f / (AMP_CTRL_MAX_GAIN - AMP_CTRL_MIN_GAIN);
    uint16_t dac_value = (uint16_t)(dac_voltage * AMP_CTRL_DAC_MAX / 3.3f);
    
    // 输出到DAC
    DAC_SetChannel1Data(DAC_Align_12b_R, dac_value);
    
    // 更新当前电压
    current_voltage = target_voltage;
    
    return target_voltage;
}

/**
 * @brief  获取当前设置的幅度
 * @param  None
 * @retval 当前幅度值 (V)
 */
float AmplitudeControl_GetVoltage(void)
{
    return current_voltage;
}

/**
 * @brief  G题专用：设置信号发生器 (频率+幅度)
 * @param  freq_hz: 频率 (Hz)
 * @param  amplitude_v: 幅度 (V)
 * @retval None
 */
void G_SetSignalGenerator(uint32_t freq_hz, float amplitude_v)
{
    // 设置频率 (AD9834)
    AD9834_SetFrequency(FREQ_REG_0, (float)freq_hz, SINE_WAVE);
    
    // 设置幅度 (DAC控制)
    AmplitudeControl_SetVoltage(amplitude_v);
    
    // 稳定时间
    Delay_ms(10);
}

/**
 * @brief  G题基本要求：信号发生器测试
 * @param  None
 * @retval None
 */
void G_BasicRequirement_Test(void)
{
    // 基本要求：100Hz-1MHz，≥3V峰峰值
    
    // 设置3V峰峰值输出
    G_SetSignalGenerator(1000000, G_VOLTAGE_BASIC);  // 1MHz, 3V
    Delay_ms(1000);  // 保持1秒供测试
    
    // 频率范围测试 (部分频率点)
    uint32_t test_frequencies[] = {100, 1000, 10000, 100000, 1000000};
    
    for (int i = 0; i < 5; i++) {
        G_SetSignalGenerator(test_frequencies[i], G_VOLTAGE_BASIC);
        Delay_ms(500);  // 每个频率保持0.5秒
    }
}

/**
 * @brief  G题控制要求：已知电路控制
 * @param  None
 * @retval None
 */
void G_ControlRequirement_Test(void)
{
    // 控制要求：1kHz，2V峰峰值
    G_SetSignalGenerator(1000, G_VOLTAGE_CONTROL);  // 1kHz, 2V
    
    // 这里可以添加闭环控制逻辑
    // 通过ADC监控已知电路输出，调整幅度使其输出2V
}

/**
 * @brief  G题扫描要求：频率扫描测试
 * @param  None
 * @retval None
 */
void G_ScanRequirement_Test(void)
{
    // 扫描要求：100Hz-3kHz，1-2V可调，步长0.1V
    
    for (uint32_t freq = 100; freq <= 3000; freq += 100) {
        for (float voltage = G_VOLTAGE_SCAN_MIN; voltage <= G_VOLTAGE_SCAN_MAX; voltage += G_VOLTAGE_SCAN_STEP) {
            G_SetSignalGenerator(freq, voltage);
            Delay_ms(100);  // 每个设置停留100ms
            
            // 这里可以进行测量和记录
        }
    }
}
