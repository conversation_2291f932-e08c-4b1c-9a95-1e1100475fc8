/**
 ******************************************************************************
 * @file    amplitude_control.h
 * <AUTHOR> - G题幅度控制
 * @version V1.0
 * @date    2025-07-31
 * @brief   信号幅度控制驱动头文件 (配合AD9834实现G题要求)
 ******************************************************************************
 * @attention
 * 
 * 硬件方案：
 * AD9834(210mV) → 可变增益放大器 → 输出(1-3V可控)
 *                      ↑
 *                 STM32 DAC控制
 * 
 * STM32引脚分配：
 * PA4 → DAC_OUT1 (幅度控制电压)
 * PA0 → ADC_IN0  (反馈监控，可选)
 * 
 * 电路参数：
 * - AD9834输出：210mV峰峰值
 * - 目标输出：1.0V - 3.0V峰峰值
 * - 所需增益：5倍 - 15倍
 * - DAC控制：0-3.3V → 增益5-15倍
 ******************************************************************************
 */

#ifndef __AMPLITUDE_CONTROL_H
#define __AMPLITUDE_CONTROL_H

#include "stm32f4xx.h"

/* ==================== 幅度控制参数定义 ==================== */
#define AMP_CTRL_MIN_VOLTAGE        1.0f       // 最小输出电压 (V)
#define AMP_CTRL_MAX_VOLTAGE        3.0f       // 最大输出电压 (V)
#define AMP_CTRL_AD9834_OUTPUT      0.21f      // AD9834输出电压 (V)
#define AMP_CTRL_MIN_GAIN           5.0f       // 最小增益倍数
#define AMP_CTRL_MAX_GAIN           15.0f      // 最大增益倍数
#define AMP_CTRL_DAC_MAX            4095       // 12位DAC最大值

/* ==================== G题专用电压定义 ==================== */
#define G_VOLTAGE_BASIC             3.0f       // 基本要求：≥3V
#define G_VOLTAGE_CONTROL           2.0f       // 控制要求：2V
#define G_VOLTAGE_SCAN_MIN          1.0f       // 扫描最小：1V
#define G_VOLTAGE_SCAN_MAX          2.0f       // 扫描最大：2V
#define G_VOLTAGE_SCAN_STEP         0.1f       // 扫描步长：0.1V

/* ==================== 函数声明 ==================== */

/**
 * @brief  幅度控制系统初始化
 * @param  None
 * @retval None
 */
void AmplitudeControl_Init(void);

/**
 * @brief  设置输出信号幅度
 * @param  target_voltage: 目标输出电压 (1.0V - 3.0V)
 * @retval 实际设置的电压值
 */
float AmplitudeControl_SetVoltage(float target_voltage);

/**
 * @brief  获取当前设置的幅度
 * @param  None
 * @retval 当前幅度值 (V)
 */
float AmplitudeControl_GetVoltage(void);

/**
 * @brief  G题专用：设置信号发生器 (频率+幅度)
 * @param  freq_hz: 频率 (Hz)
 * @param  amplitude_v: 幅度 (V)
 * @retval None
 */
void G_SetSignalGenerator(uint32_t freq_hz, float amplitude_v);

/**
 * @brief  G题基本要求：信号发生器测试
 * @param  None
 * @retval None
 */
void G_BasicRequirement_Test(void);

/**
 * @brief  G题控制要求：已知电路控制
 * @param  None
 * @retval None
 */
void G_ControlRequirement_Test(void);

/**
 * @brief  G题扫描要求：频率扫描测试
 * @param  None
 * @retval None
 */
void G_ScanRequirement_Test(void);

#endif /* __AMPLITUDE_CONTROL_H */
